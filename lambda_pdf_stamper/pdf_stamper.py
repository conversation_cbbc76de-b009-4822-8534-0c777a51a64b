import fitz
import os
import logging
from PIL import Image
import tempfile
from datetime import datetime
import uuid

logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("pdf-stamper")

class PDFStamper:
    def __init__(self, output_directory="output"):
        self.output_directory = output_directory
        self._ensure_output_directory()
    
    def _ensure_output_directory(self):
        """Asegurar que el directorio de salida existe"""
        if not os.path.exists(self.output_directory):
            os.makedirs(self.output_directory)
            logger.info(f"Directorio de salida creado: {self.output_directory}")
    
    def stamp_pdf(self, pdf_path, stamp_image_path, x, y, width, height, page_number, output_filename=None, rotate=False):
        """
        Estampa un PDF con una imagen PNG en la posición especificada
        
        Args:
            pdf_path (str): Ruta al archivo PDF a estampar
            stamp_image_path (str): Ruta a la imagen PNG de la estampilla
            x (int): Posición X en píxeles (origen superior izquierdo)
            y (int): Posición Y en píxeles (origen superior izquierdo)
            width (int): Ancho de la estampilla en píxeles
            height (int): Alto de la estampilla en píxeles
            page_number (int): Número de página a estampar (comienza en 1)
            output_filename (str, optional): Nombre del archivo de salida. Si es None, se genera automáticamente.
            rotate (bool, optional): Si es True, rota la estampilla 90 grados antihorario. Por defecto False.
        
        Returns:
            str: Ruta completa del archivo PDF estampado generado
        
        Raises:
            FileNotFoundError: Si el PDF o la estampilla no existen
            ValueError: Si los parámetros son inválidos
            Exception: Para otros errores durante el procesamiento
        """
        try:
            # Validar archivos de entrada
            if not os.path.exists(pdf_path):
                raise FileNotFoundError(f"Archivo PDF no encontrado: {pdf_path}")
            
            if not os.path.exists(stamp_image_path):
                raise FileNotFoundError(f"Archivo de estampilla no encontrado: {stamp_image_path}")
            
            # Validar parámetros
            if page_number < 1:
                raise ValueError("El número de página debe ser mayor a 0")
            
            if width <= 0 or height <= 0:
                raise ValueError("El ancho y alto deben ser valores positivos")
            
            # Abrir el documento PDF
            pdf_document = fitz.open(pdf_path)
            
            # Validar que la página existe
            if page_number > len(pdf_document):
                raise ValueError(f"La página {page_number} no existe. El documento tiene {len(pdf_document)} páginas")
            
            # Obtener la página (PyMuPDF usa índice basado en 0)
            page = pdf_document[page_number - 1]
            
            # Obtener dimensiones de la página en puntos y píxeles
            page_rect = page.rect
            page_width_pts = page_rect.width
            page_height_pts = page_rect.height
            page_width_px = int(page_width_pts)  # Para simplificar, asumimos 72 DPI
            page_height_px = int(page_height_pts)
            
            # Ajustar dimensiones si la imagen está rotada
            final_width = width
            final_height = height
            if rotate:
                final_width = height  # Al rotar 90°, ancho se convierte en alto
                final_height = width  # y alto se convierte en ancho
                logger.info(f"Rotación aplicada: dimensiones cambiadas de {width}x{height} a {final_width}x{final_height}")
            
            # Normalizar coordenadas para que no se salgan de los márgenes
            normalized_x, normalized_y = self._normalize_coordinates(
                x, y, final_width, final_height, page_width_px, page_height_px
            )
            
            if normalized_x != x or normalized_y != y:
                logger.info(f"Coordenadas normalizadas: ({x}, {y}) -> ({normalized_x}, {normalized_y})")
            
            # Convertir coordenadas de píxeles a puntos PDF
            # PyMuPDF usa 72 DPI por defecto
            dpi = 72
            x_pts = (normalized_x * 72) / dpi
            y_pts = (normalized_y * 72) / dpi
            width_pts = (final_width * 72) / dpi
            height_pts = (final_height * 72) / dpi
            
            # Preparar el rectángulo donde se insertará la estampilla
            stamp_rect = fitz.Rect(x_pts, y_pts, x_pts + width_pts, y_pts + height_pts)
            
            # Verificar que la imagen de estampilla es válida y procesarla si es necesario
            processed_image_path = stamp_image_path
            temp_rotated_file = None
            
            try:
                with Image.open(stamp_image_path) as img:
                    # Verificar que la imagen tiene el formato correcto
                    if img.format not in ['PNG', 'JPEG', 'JPG']:
                        logger.warning(f"Formato de imagen inesperado: {img.format}")
                    
                    # Si necesita rotación, crear imagen rotada temporal
                    if rotate:
                        # Rotar 90 grados antihorario (counter-clockwise)
                        rotated_img = img.rotate(90, expand=True)
                        
                        # Crear archivo temporal para la imagen rotada
                        temp_rotated_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
                        rotated_img.save(temp_rotated_file.name, 'PNG')
                        processed_image_path = temp_rotated_file.name
                        temp_rotated_file.close()
                        
                        logger.info(f"Imagen rotada guardada temporalmente: {processed_image_path}")
                        
            except Exception as img_error:
                raise ValueError(f"Error al validar/procesar la imagen de estampilla: {str(img_error)}")
            
            try:
                # Insertar la imagen en la página
                page.insert_image(stamp_rect, filename=processed_image_path, overlay=True)
            finally:
                # Limpiar archivo temporal si se creó
                if temp_rotated_file and os.path.exists(processed_image_path) and processed_image_path != stamp_image_path:
                    os.unlink(processed_image_path)
            
            # Generar nombre de archivo de salida si no se proporcionó
            if output_filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                unique_id = uuid.uuid4().hex[:8]
                base_name = os.path.splitext(os.path.basename(pdf_path))[0]
                output_filename = f"{base_name}_estampado_{timestamp}_{unique_id}.pdf"
            
            # Construir ruta completa de salida
            output_path = os.path.join(self.output_directory, output_filename)
            
            # Guardar el PDF modificado
            pdf_document.save(output_path)
            pdf_document.close()
            
            logger.info(f"PDF estampado guardado exitosamente: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error al estampar PDF: {str(e)}", exc_info=True)
            raise
    
    def _normalize_coordinates(self, x, y, width, height, page_width, page_height):
        """
        Normaliza las coordenadas para que la estampilla no se salga de los márgenes de la página
        
        Args:
            x, y (int): Coordenadas originales
            width, height (int): Dimensiones de la estampilla
            page_width, page_height (int): Dimensiones de la página en píxeles
            
        Returns:
            tuple: (x_normalizado, y_normalizado)
        """
        # Asegurar que las coordenadas no sean negativas
        normalized_x = max(0, x)
        normalized_y = max(0, y)
        
        # Asegurar que la estampilla no se salga del ancho de la página
        if normalized_x + width > page_width:
            normalized_x = max(0, page_width - width)
        
        # Asegurar que la estampilla no se salga del alto de la página
        if normalized_y + height > page_height:
            normalized_y = max(0, page_height - height)
        
        # Si la estampilla es más grande que la página, centrarla
        if width > page_width:
            normalized_x = (page_width - width) // 2
            logger.warning(f"Estampilla más ancha que la página. Centrada horizontalmente.")
        
        if height > page_height:
            normalized_y = (page_height - height) // 2
            logger.warning(f"Estampilla más alta que la página. Centrada verticalmente.")
        
        return int(normalized_x), int(normalized_y)
    
    def stamp_pdf_from_bytes(self, pdf_bytes, stamp_image_bytes, x, y, width, height, page_number, output_filename=None, rotate=False):
        """
        Estampa un PDF desde bytes con una imagen desde bytes
        
        Args:
            pdf_bytes (bytes): Contenido del PDF en bytes
            stamp_image_bytes (bytes): Contenido de la imagen PNG en bytes
            x (int): Posición X en píxeles
            y (int): Posición Y en píxeles  
            width (int): Ancho de la estampilla en píxeles
            height (int): Alto de la estampilla en píxeles
            page_number (int): Número de página a estampar (comienza en 1)
            output_filename (str, optional): Nombre del archivo de salida
            rotate (bool, optional): Si es True, rota la estampilla 90 grados antihorario. Por defecto False.
        
        Returns:
            str: Ruta completa del archivo PDF estampado generado
        """
        try:
            # Crear archivos temporales
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_pdf:
                temp_pdf.write(pdf_bytes)
                temp_pdf_path = temp_pdf.name
            
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_stamp:
                temp_stamp.write(stamp_image_bytes)
                temp_stamp_path = temp_stamp.name
            
            try:
                # Usar el método principal con archivos temporales
                result = self.stamp_pdf(
                    temp_pdf_path, 
                    temp_stamp_path, 
                    x, y, width, height, 
                    page_number, 
                    output_filename,
                    rotate
                )
                return result
            finally:
                # Limpiar archivos temporales
                if os.path.exists(temp_pdf_path):
                    os.unlink(temp_pdf_path)
                if os.path.exists(temp_stamp_path):
                    os.unlink(temp_stamp_path)
                    
        except Exception as e:
            logger.error(f"Error al estampar PDF desde bytes: {str(e)}", exc_info=True)
            raise
    
    def get_pdf_info(self, pdf_path):
        """
        Obtiene información básica del PDF
        
        Args:
            pdf_path (str): Ruta al archivo PDF
            
        Returns:
            dict: Información del PDF (número de páginas, dimensiones, etc.)
        """
        try:
            if not os.path.exists(pdf_path):
                raise FileNotFoundError(f"Archivo PDF no encontrado: {pdf_path}")
            
            pdf_document = fitz.open(pdf_path)
            
            info = {
                "page_count": len(pdf_document),
                "pages": []
            }
            
            for page_num in range(len(pdf_document)):
                page = pdf_document[page_num]
                page_rect = page.rect
                
                page_info = {
                    "page_number": page_num + 1,
                    "width_pts": page_rect.width,
                    "height_pts": page_rect.height,
                    "width_px": int(page_rect.width * 72 / 72),  # Conversión a píxeles
                    "height_px": int(page_rect.height * 72 / 72)
                }
                info["pages"].append(page_info)
            
            pdf_document.close()
            return info
            
        except Exception as e:
            logger.error(f"Error al obtener información del PDF: {str(e)}", exc_info=True)
            raise 